<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sub4Short-Plus</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
    <!-- Background decorative elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full floating-animation"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-10 rounded-full floating-animation" style="animation-delay: -1s;"></div>
    </div>

    <div class="w-full max-w-lg relative z-10" x-data="loginForm()">
        <!-- Main Container -->
        <div class="glass-effect rounded-3xl shadow-2xl p-10">
            <!-- Logo Section -->
            <div class="text-center mb-10">
                <!-- Logo SVG -->
                <div class="mx-auto mb-6 w-24 h-24 relative">
                    <svg viewBox="0 0 100 100" class="w-full h-full">
                        <defs>
                            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" class="drop-shadow-lg"/>
                        <path d="M30 35 L45 50 L70 25" stroke="white" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M30 55 L45 70 L70 45" stroke="white" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold text-gray-800 mb-3">Sub4Short-Plus</h1>
                <p class="text-gray-600 text-lg">Selamat datang kembali</p>
                <div class="w-16 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto mt-4 rounded-full"></div>
            </div>

            <!-- Login Form -->
            <form @submit.prevent="submitForm" class="space-y-7">
                <!-- Username/Email Input -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 block">Username atau Email</label>
                    <div class="relative group">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400 group-focus-within:text-purple-500 transition-colors"></i>
                        </div>
                        <input
                            type="text"
                            x-model="form.username"
                            placeholder="Masukkan username atau email"
                            required
                            class="w-full pl-12 pr-4 py-4 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-white transition-all duration-200 text-gray-800"
                        >
                    </div>
                </div>

                <!-- Password Input -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 block">Password</label>
                    <div class="relative group">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400 group-focus-within:text-purple-500 transition-colors"></i>
                        </div>
                        <input
                            :type="showPassword ? 'text' : 'password'"
                            x-model="form.password"
                            placeholder="Masukkan password"
                            required
                            class="w-full pl-12 pr-12 py-4 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-white transition-all duration-200 text-gray-800"
                        >
                        <button
                            type="button"
                            @click="showPassword = !showPassword"
                            class="absolute inset-y-0 right-0 pr-4 flex items-center hover:bg-gray-100 rounded-r-xl transition-colors"
                        >
                            <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-gray-400 hover:text-purple-500 transition-colors"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between pt-2">
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="remember-me"
                            x-model="form.remember"
                            class="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded transition-colors"
                        >
                        <label for="remember-me" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer">
                            Ingat saya
                        </label>
                    </div>
                    <div class="text-right">
                        <a href="{{ route('forgot-password') }}" class="text-purple-600 hover:text-purple-800 text-sm font-medium transition-colors hover:underline">
                            Lupa Password?
                        </a>
                    </div>
                </div>

                <!-- Login Button -->
                <button
                    type="submit"
                    :disabled="loading"
                    class="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                    <span x-show="!loading" class="flex items-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Masuk
                    </span>
                    <div x-show="loading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Memproses...
                    </div>
                </button>
            </form>

            <!-- Register Link -->
            <div class="mt-8 text-center">
                <p class="text-gray-600">
                    Belum punya akun?
                    <a href="{{ route('register') }}" class="text-blue-600 hover:text-blue-800 font-semibold transition-colors">
                        Daftar disini
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script>
        function loginForm() {
            return {
                form: {
                    username: '',
                    password: '',
                    remember: false
                },
                loading: false,
                showPassword: false,

                async submitForm() {
                    this.loading = true;

                    // Simulasi proses login
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Menampilkan status remember me untuk demo
                    const rememberStatus = this.form.remember ? 'dengan Remember Me' : 'tanpa Remember Me';
                    alert(`Login berhasil ${rememberStatus}! (Demo)`);

                    this.loading = false;
                }
            }
        }
    </script>
</body>
</html>