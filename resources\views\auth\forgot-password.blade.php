<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lupa Password - Sub4Short-Plus</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md" x-data="forgotPasswordForm()">
        <!-- Container dengan background putih -->
        <div class="bg-white rounded-2xl shadow-2xl p-8">
            <!-- Logo Section -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center shadow-lg">
                    <i class="fas fa-key text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Sub4Short-Plus</h1>
                <p class="text-gray-600">Reset password Anda</p>
            </div>

            <!-- Info Text -->
            <div class="mb-6 text-center">
                <p class="text-gray-600 text-sm">
                    Masukkan email Anda dan kami akan mengirimkan link untuk reset password
                </p>
            </div>

            <!-- Forgot Password Form -->
            <form @submit.prevent="submitForm" class="space-y-6">
                <!-- Email Input -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-envelope text-gray-400"></i>
                    </div>
                    <input 
                        type="email" 
                        x-model="form.email"
                        placeholder="Email"
                        required
                        class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    >
                </div>

                <!-- Submit Button -->
                <button 
                    type="submit" 
                    :disabled="loading"
                    class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
                >
                    <span x-show="!loading">Kirim Link Reset</span>
                    <div x-show="loading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Mengirim...
                    </div>
                </button>
            </form>

            <!-- Back to Login Link -->
            <div class="mt-8 text-center">
                <p class="text-gray-600">
                    Ingat password Anda? 
                    <a href="{{ route('login') }}" class="text-orange-600 hover:text-orange-800 font-semibold transition-colors">
                        Kembali ke Login
                    </a>
                </p>
            </div>

            <!-- Register Link -->
            <div class="mt-4 text-center">
                <p class="text-gray-600 text-sm">
                    Belum punya akun? 
                    <a href="{{ route('register') }}" class="text-orange-600 hover:text-orange-800 font-semibold transition-colors">
                        Daftar disini
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script>
        function forgotPasswordForm() {
            return {
                form: {
                    email: ''
                },
                loading: false,

                async submitForm() {
                    this.loading = true;
                    
                    // Simulasi proses forgot password
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    alert('Link reset password telah dikirim ke email Anda! (Demo)');
                    this.loading = false;
                    
                    // Reset form
                    this.form.email = '';
                }
            }
        }
    </script>
</body>
</html>
